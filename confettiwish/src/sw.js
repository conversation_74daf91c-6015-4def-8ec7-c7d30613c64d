import { precacheAndRoute } from 'workbox-precaching'
import { registerRoute } from 'workbox-routing'
import { StaleWhileRevalidate, CacheFirst } from 'workbox-strategies'
import { clientsClaim } from 'workbox-core'

// Use with precache injection
precacheAndRoute(self.__WB_MANIFEST)
self.skipWaiting()
clientsClaim()

// Cache images
registerRoute(
  ({ request }) => request.destination === 'image',
  new CacheFirst({
    cacheName: 'images',
    plugins: [
      {
        // Cache for up to 30 days
        expiration: {
          maxAgeSeconds: 30 * 24 * 60 * 60,
        },
      },
    ],
  })
)

// Cache API responses
registerRoute(
  ({ url }) => url.pathname.startsWith('/api/'),
  new StaleWhileRevalidate({
    cacheName: 'api-cache',
  })
)

// Cache Supabase responses
registerRoute(
  ({ url }) => url.hostname.includes('supabase.co'),
  new StaleWhileRevalidate({
    cacheName: 'supabase-cache',
  })
)

// Handle push notifications
self.addEventListener('push', (event) => {
  if (!event.data) {
    console.log('Push event but no data')
    return
  }

  let data
  try {
    data = event.data.json()
  } catch (e) {
    console.error('Error parsing push data:', e)
    data = {
      title: 'New Notification',
      body: event.data.text(),
      data: { url: '/' }
    }
  }

  // Track notification analytics
  const analyticsData = {
    type: data.type || 'unknown',
    timestamp: new Date().toISOString(),
    received: true
  }

  // Store notification in IndexedDB for offline access
  if (data.notificationId) {
    // This would be implemented with IndexedDB
    console.log('Storing notification:', data.notificationId)
  }

  const options = {
    body: data.body,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    data: {
      ...data.data,
      analyticsData
    },
    actions: data.actions || [],
    // Add vibration pattern
    vibrate: [100, 50, 100],
    // Add timestamp
    timestamp: data.timestamp || Date.now()
  }

  // Show notification
  event.waitUntil(
    self.registration.showNotification(data.title, options)
  )
})

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  // Close the notification
  event.notification.close()

  // Get action (if any)
  const action = event.action
  const notification = event.notification
  const data = notification.data || {}

  // Track notification click
  if (data.analyticsData) {
    data.analyticsData.clicked = true
    data.analyticsData.clickedAt = new Date().toISOString()
    data.analyticsData.action = action || 'default'

    // This would send analytics data to the server
    console.log('Notification clicked:', data.analyticsData)
  }

  // Determine target URL based on action
  let targetUrl = '/'

  if (action) {
    // Handle specific actions
    switch (action) {
      case 'view':
        targetUrl = data.url || '/'
        break
      case 'dismiss':
        // Just close the notification
        return
      default:
        // Use the action-specific URL if available
        targetUrl = data.actionUrls?.[action] || data.url || '/'
    }
  } else {
    // Default action (notification body click)
    targetUrl = data.url || '/'
  }

  // Mark notification as read in the database when clicked
  if (data.notificationId) {
    // This would be implemented to call the backend
    console.log('Marking notification as read:', data.notificationId)
  }

  // Focus or open window
  event.waitUntil(
    clients.matchAll({ type: 'window' }).then((clientList) => {
      // Check if a window is already open at the target URL
      const hadWindowToFocus = clientList.some((client) => {
        return client.url === targetUrl && 'focus' in client
          ? (client.focus(), true)
          : false
      })

      // If no window is open at the target URL, open a new one
      if (!hadWindowToFocus && clients.openWindow) {
        return clients.openWindow(targetUrl)
      }
    })
  )
})
