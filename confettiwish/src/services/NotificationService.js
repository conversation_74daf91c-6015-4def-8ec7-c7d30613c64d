import { Capacitor } from '@capacitor/core'
import { PushNotifications } from '@capacitor/push-notifications'
import { supabase } from '@/lib/supabase'

export class NotificationService {
  /**
   * Check if push notifications are available
   * @returns {boolean} True if push notifications are available
   */
  static isPushAvailable() {
    return Capacitor.isPluginAvailable('PushNotifications')
  }

  /**
   * Check if web notifications are available
   * @returns {boolean} True if web notifications are available
   */
  static isWebNotificationAvailable() {
    return 'Notification' in window
  }

  /**
   * Initialize push notifications for mobile
   * @returns {Promise<void>}
   */
  static async initializePushNotifications() {
    if (!this.isPushAvailable()) {
      console.log('Push notifications not available')
      return
    }

    try {
      // Request permission
      const result = await PushNotifications.requestPermissions()

      if (result.receive === 'granted') {
        // Register with FCM or APNS
        await PushNotifications.register()
      }
    } catch (error) {
      console.error('Error initializing push notifications:', error)
    }
  }

  /**
   * Send a test notification
   * @param {string} userId - The user ID
   */
  static async sendTestNotification(userId) {
    if (!userId) {
      console.error('No user ID provided for test notification')
      return false
    }

    try {
      const response = await supabase.functions.invoke('send-notifications', {
        body: {
          type: 'test',
          userId: userId,
          data: {
            title: 'Test Notification',
            body: 'This is a test notification from ConfettiWish',
            data: {
              url: '/',
              type: 'test'
            }
          }
        }
      })

      if (response.error) {
        console.error('Error sending test notification:', response.error)
        return false
      }

      console.log('Test notification sent successfully')
      return true
    } catch (error) {
      console.error('Error sending test notification:', error)
      return false
    }
  }

  /**
   * Initialize web notifications using Firebase
   * @param {string} userId - The user ID
   * @returns {Promise<boolean>} True if permission is granted
   */
  static async initializeWebNotifications(userId) {
    console.log('Initializing web notifications with Firebase...')

    if (!userId) {
      console.error('No user ID provided for web notification initialization')
      return false
    }

    // Check if permission is already granted
    if (Notification.permission === 'granted') {
      console.log('Permission already granted, registering Firebase token...')

      try {
        // Initialize Firebase and get FCM token
        const fcmToken = await this.getFirebaseToken()
        if (fcmToken) {
          await this.registerDeviceToken(fcmToken, 'web', userId)
          return true
        } else {
          console.error('Failed to get Firebase FCM token')
          return false
        }
      } catch (error) {
        console.error('Error registering Firebase web notification token:', error)
        return false
      }
    }

    // Permission needs to be requested from user gesture
    return new Promise((resolve) => {
      const button = document.createElement('button')
      button.style.display = 'none'
      button.onclick = async () => {
        try {
          const permission = await Notification.requestPermission()
          console.log('Permission request result:', permission)

          if (permission === 'granted') {
            // Initialize Firebase and get FCM token
            const fcmToken = await this.getFirebaseToken()
            if (fcmToken) {
              await this.registerDeviceToken(fcmToken, 'web', userId)
              resolve(true)
            } else {
              console.error('Failed to get Firebase FCM token')
              resolve(false)
            }
          } else {
            console.log('Notification permission denied')
            resolve(false)
          }
        } catch (error) {
          console.error('Error requesting notification permission:', error)
          resolve(false)
        }
      }

      // Simulate click to trigger permission request
      button.click()
    })
  }

  /**
   * Get Firebase FCM token for web notifications
   * @returns {Promise<string|null>} The FCM token or null if failed
   */
  static async getFirebaseToken() {
    try {
      // Dynamically import Firebase modules
      const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js')
      const { getMessaging, getToken } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging.js')

      // Firebase config
      const firebaseConfig = {
        apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
        authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
        projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
        storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
        messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
        appId: import.meta.env.VITE_FIREBASE_APP_ID
      }

      // Initialize Firebase
      const app = initializeApp(firebaseConfig)
      const messaging = getMessaging(app)

      // Register the Firebase messaging service worker
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
            scope: '/firebase-cloud-messaging-push-scope'
          })
          console.log('Firebase messaging service worker registered:', registration.scope)
        } catch (swError) {
          console.warn('Firebase messaging service worker registration failed:', swError)
          // Continue anyway, Firebase might use the default service worker
        }
      }

      // Get FCM token
      const token = await getToken(messaging, {
        vapidKey: import.meta.env.VITE_FIREBASE_VAPID_KEY
      })

      console.log('Firebase FCM token obtained:', token ? 'success' : 'failed')
      return token
    } catch (error) {
      console.error('Error getting Firebase FCM token:', error)
      return null
    }
  }

  /**
   * Register device token with backend
   * @param {string} token - The device token
   * @param {string} platform - The platform (ios, android, web)
   * @param {string} userId - The user ID
   * @returns {Promise<void>}
   */
  static async registerDeviceToken(token, platform, userId) {
    try {
      const { error } = await supabase
        .from('device_tokens')
        .upsert(
          {
            user_id: userId,
            token: token,
            platform: platform,
            created_at: new Date().toISOString()
          },
          { onConflict: 'token' }
        )

      if (error) throw error
      console.log('Device token registered successfully')
    } catch (error) {
      console.error('Error registering device token:', error)
      throw error
    }
  }

  /**
   * Send a local notification (mobile only)
   * @param {Object} notification - The notification object
   * @returns {Promise<void>}
   */
  static async sendLocalNotification(notification) {
    if (!this.isPushAvailable()) {
      console.log('Push notifications not available')
      return
    }

    try {
      await PushNotifications.schedule({
        notifications: [
          {
            title: notification.title,
            body: notification.body,
            id: notification.id || Math.floor(Math.random() * 100000),
            extra: notification.data
          }
        ]
      })
    } catch (error) {
      console.error('Error sending local notification:', error)
    }
  }

  /**
   * Send a web notification
   * @param {Object} notification - The notification object
   * @returns {void}
   */
  static sendWebNotification(notification) {
    if (!this.isWebNotificationAvailable()) {
      console.log('Web notifications not available')
      return
    }

    if (Notification.permission !== 'granted') {
      console.log('Web notification permission not granted')
      return
    }

    try {
      const options = {
        body: notification.body,
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        data: notification.data
      }

      new Notification(notification.title, options)
    } catch (error) {
      console.error('Error sending web notification:', error)
    }
  }

  /**
   * Setup push notification listeners for mobile
   * @param {Function} onNotificationReceived - Callback when notification is received
   * @param {Function} onTokenReceived - Callback when token is received
   * @returns {void}
   */
  static setupPushListeners(onNotificationReceived, onTokenReceived) {
    if (!this.isPushAvailable()) {
      return
    }

    // On registration
    PushNotifications.addListener('registration', (token) => {
      console.log('Push registration success, token:', token.value)
      if (onTokenReceived) {
        onTokenReceived(token.value)
      }
    })

    // On notification received
    PushNotifications.addListener('pushNotificationReceived', (notification) => {
      console.log('Push notification received:', notification)
      if (onNotificationReceived) {
        onNotificationReceived(notification)
      }
    })

    // On notification action
    PushNotifications.addListener('pushNotificationActionPerformed', (action) => {
      console.log('Push notification action performed:', action)
      if (onNotificationReceived) {
        onNotificationReceived(action.notification)
      }
    })
  }
}
