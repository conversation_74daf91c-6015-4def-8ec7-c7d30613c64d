import { supabase } from '@/lib/supabase'

export class WebPushService {
  static async register() {
    try {
      // Check if the browser supports push notifications
      if (!('serviceWorker' in navigator)) {
        console.log('Service workers aren\'t supported in this browser.');
        return null;
      }

      // Register service worker
      const registration = await navigator.serviceWorker.register('/service-worker.js');
      console.log('Service Worker registered with scope:', registration.scope);

      // Get the push subscription using Firebase VAPID key
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: import.meta.env.VITE_FIREBASE_VAPID_KEY
      });

      // Register subscription with Supabase
      await this.registerToken(subscription);
      return subscription;
    } catch (error) {
      console.error('Error registering push:', error);
      return null;
    }
  }

  static async registerToken(subscription) {
    try {
      const { error } = await supabase
        .from('device_tokens')
        .upsert(
          {
            token: JSON.stringify(subscription),
            platform: 'web',
            user_id: supabase.auth.getUser().data.user?.id,
            created_at: new Date().toISOString()
          },
          { onConflict: 'token' }
        );

      if (error) throw error;
      console.log('Subscription registered successfully');
    } catch (error) {
      console.error('Error registering subscription:', error);
    }
  }

  static async unregisterToken(subscription) {
    try {
      const { error } = await supabase
        .from('device_tokens')
        .delete()
        .eq('token', JSON.stringify(subscription));

      if (error) throw error;
      console.log('Subscription unregistered successfully');
    } catch (error) {
      console.error('Error unregistering subscription:', error);
    }
  }
}
