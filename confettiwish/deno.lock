{"version": "5", "redirects": {"https://esm.sh/@fastify/busboy@^3.0.0?target=denonext": "https://esm.sh/@fastify/busboy@3.1.1?target=denonext", "https://esm.sh/@supabase/node-fetch@^2.6.14?target=denonext": "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext", "https://esm.sh/@supabase/supabase-js@2": "https://esm.sh/@supabase/supabase-js@2.49.8", "https://esm.sh/@types/bn.js@~4.11.6/index.d.ts": "https://esm.sh/@types/bn.js@4.11.6/index.d.ts", "https://esm.sh/@types/debug@~4.1.12/index.d.ts": "https://esm.sh/@types/debug@4.1.12/index.d.ts", "https://esm.sh/@types/extend@~3.0.4/index.d.ts": "https://esm.sh/@types/extend@3.0.4/index.d.ts", "https://esm.sh/@types/json-bigint@~1.0.4/index.d.ts": "https://esm.sh/@types/json-bigint@1.0.4/index.d.ts", "https://esm.sh/@types/jwa@~2.0.3/index.d.ts": "https://esm.sh/@types/jwa@2.0.3/index.d.ts", "https://esm.sh/@types/minimalistic-assert@~1.0.3/index.d.ts": "https://esm.sh/@types/minimalistic-assert@1.0.3/index.d.ts", "https://esm.sh/@types/node-forge@~1.3.11/index.d.ts": "https://esm.sh/@types/node-forge@1.3.11/index.d.ts", "https://esm.sh/@types/safer-buffer@~2.1.3/index.d.ts": "https://esm.sh/@types/safer-buffer@2.1.3/index.d.ts", "https://esm.sh/@types/urlsafe-base64@~1.0.31/index.d.ts": "https://esm.sh/@types/urlsafe-base64@1.0.31/index.d.ts", "https://esm.sh/@types/uuid@~9.0.8/index.d.mts": "https://esm.sh/@types/uuid@9.0.8/index.d.mts", "https://esm.sh/@types/web-push@~3.6.4/index.d.ts": "https://esm.sh/@types/web-push@3.6.4/index.d.ts", "https://esm.sh/@types/ws@~8.18.1/index.d.mts": "https://esm.sh/@types/ws@8.18.1/index.d.mts", "https://esm.sh/agent-base@^7.1.2?target=denonext": "https://esm.sh/agent-base@7.1.3?target=denonext", "https://esm.sh/asn1.js@^5.3.0?target=denonext": "https://esm.sh/asn1.js@5.4.1?target=denonext", "https://esm.sh/base64-js@^1.3.0?target=denonext": "https://esm.sh/base64-js@1.5.1?target=denonext", "https://esm.sh/bignumber.js@^9.0.0?target=denonext": "https://esm.sh/bignumber.js@9.3.0?target=denonext", "https://esm.sh/bn.js@^4.0.0?target=denonext": "https://esm.sh/bn.js@4.12.2?target=denonext", "https://esm.sh/buffer-equal-constant-time@^1.0.1?target=denonext": "https://esm.sh/buffer-equal-constant-time@1.0.1?target=denonext", "https://esm.sh/bufferutil@^4.0.1?target=denonext": "https://esm.sh/bufferutil@4.0.9?target=denonext", "https://esm.sh/debug@4?target=denonext": "https://esm.sh/debug@4.4.1?target=denonext", "https://esm.sh/ecdsa-sig-formatter@^1.0.11?target=denonext": "https://esm.sh/ecdsa-sig-formatter@1.0.11?target=denonext", "https://esm.sh/extend@^3.0.2?target=denonext": "https://esm.sh/extend@3.0.2?target=denonext", "https://esm.sh/firebase-admin/app": "https://esm.sh/firebase-admin@13.4.0/app", "https://esm.sh/firebase-admin/messaging": "https://esm.sh/firebase-admin@13.4.0/messaging", "https://esm.sh/gaxios@^6.0.0?target=denonext": "https://esm.sh/gaxios@6.7.1?target=denonext", "https://esm.sh/gaxios@^6.1.1?target=denonext": "https://esm.sh/gaxios@6.7.1?target=denonext", "https://esm.sh/gcp-metadata@^6.1.0?target=denonext": "https://esm.sh/gcp-metadata@6.1.1?target=denonext", "https://esm.sh/google-auth-library@^9.14.2?target=denonext": "https://esm.sh/google-auth-library@9.15.1?target=denonext", "https://esm.sh/google-logging-utils@^0.0.2?target=denonext": "https://esm.sh/google-logging-utils@0.0.2?target=denonext", "https://esm.sh/gtoken@^7.0.0?target=denonext": "https://esm.sh/gtoken@7.1.0?target=denonext", "https://esm.sh/https-proxy-agent@^7.0.0?target=denonext": "https://esm.sh/https-proxy-agent@7.0.6?target=denonext", "https://esm.sh/https-proxy-agent@^7.0.1?target=denonext": "https://esm.sh/https-proxy-agent@7.0.6?target=denonext", "https://esm.sh/is-stream@^2.0.0?target=denonext": "https://esm.sh/is-stream@2.0.1?target=denonext", "https://esm.sh/json-bigint@^1.0.0?target=denonext": "https://esm.sh/json-bigint@1.0.0?target=denonext", "https://esm.sh/jwa@^2.0.0?target=denonext": "https://esm.sh/jwa@2.0.1?target=denonext", "https://esm.sh/jws@^4.0.0?target=denonext": "https://esm.sh/jws@4.0.0?target=denonext", "https://esm.sh/minimalistic-assert@^1.0.0?target=denonext": "https://esm.sh/minimalistic-assert@1.0.1?target=denonext", "https://esm.sh/ms@^2.1.3?target=denonext": "https://esm.sh/ms@2.1.3?target=denonext", "https://esm.sh/node-forge@^1.3.1?target=denonext": "https://esm.sh/node-forge@1.3.1?target=denonext", "https://esm.sh/node-gyp-build@^4.3.0?target=denonext": "https://esm.sh/node-gyp-build@4.8.4?target=denonext", "https://esm.sh/safe-buffer@^5.0.1?target=denonext": "https://esm.sh/safe-buffer@5.2.1?target=denonext", "https://esm.sh/safer-buffer@^2.1.0?target=denonext": "https://esm.sh/safer-buffer@2.1.2?target=denonext", "https://esm.sh/supports-color?target=denonext": "https://esm.sh/supports-color@10.0.0?target=denonext", "https://esm.sh/tr46@~0.0.3?target=denonext": "https://esm.sh/tr46@0.0.3?target=denonext", "https://esm.sh/urlsafe-base64@~1.0.0?target=denonext": "https://esm.sh/urlsafe-base64@1.0.0?target=denonext", "https://esm.sh/utf-8-validate@%3E=5.0.2?target=denonext": "https://esm.sh/utf-8-validate@6.0.5?target=denonext", "https://esm.sh/uuid@^9.0.1?target=denonext": "https://esm.sh/uuid@9.0.1?target=denonext", "https://esm.sh/webidl-conversions@^3.0.0?target=denonext": "https://esm.sh/webidl-conversions@3.0.1?target=denonext", "https://esm.sh/whatwg-url@^5.0.0?target=denonext": "https://esm.sh/whatwg-url@5.0.0?target=denonext", "https://esm.sh/ws@^8.18.0?target=denonext": "https://esm.sh/ws@8.18.2?target=denonext"}, "remote": {"https://deno.land/std@0.168.0/async/abortable.ts": "80b2ac399f142cc528f95a037a7d0e653296352d95c681e284533765961de409", "https://deno.land/std@0.168.0/async/deadline.ts": "2c2deb53c7c28ca1dda7a3ad81e70508b1ebc25db52559de6b8636c9278fd41f", "https://deno.land/std@0.168.0/async/debounce.ts": "60301ffb37e730cd2d6f9dadfd0ecb2a38857681bd7aaf6b0a106b06e5210a98", "https://deno.land/std@0.168.0/async/deferred.ts": "77d3f84255c3627f1cc88699d8472b664d7635990d5358c4351623e098e917d6", "https://deno.land/std@0.168.0/async/delay.ts": "5a9bfba8de38840308a7a33786a0155a7f6c1f7a859558ddcec5fe06e16daf57", "https://deno.land/std@0.168.0/async/mod.ts": "7809ad4bb223e40f5fdc043e5c7ca04e0e25eed35c32c3c32e28697c553fa6d9", "https://deno.land/std@0.168.0/async/mux_async_iterator.ts": "770a0ff26c59f8bbbda6b703a2235f04e379f73238e8d66a087edc68c2a2c35f", "https://deno.land/std@0.168.0/async/pool.ts": "6854d8cd675a74c73391c82005cbbe4cc58183bddcd1fbbd7c2bcda42b61cf69", "https://deno.land/std@0.168.0/async/retry.ts": "e8e5173623915bbc0ddc537698fa418cf875456c347eda1ed453528645b42e67", "https://deno.land/std@0.168.0/async/tee.ts": "3a47cc4e9a940904fd4341f0224907e199121c80b831faa5ec2b054c6d2eff5e", "https://deno.land/std@0.168.0/http/server.ts": "e99c1bee8a3f6571ee4cdeb2966efad465b8f6fe62bec1bdb59c1f007cc4d155", "https://esm.sh/@fastify/busboy@3.1.1/denonext/busboy.mjs": "e6b358ba9d6f0e0dca970553b61df2b69da2f84f5fc657c5e09a88ad40e548a4", "https://esm.sh/@fastify/busboy@3.1.1?target=denonext": "6164205bf0af8dfe5405cdffd6b64be304c297a4089acd61640b9a3fab35b061", "https://esm.sh/@supabase/auth-js@2.69.1/denonext/auth-js.mjs": "fb31c3925437753f5a8a90fc57ea24dc5b68b2b295e696123b1b6a635b7b3ada", "https://esm.sh/@supabase/functions-js@2.4.4/denonext/functions-js.mjs": "7adeb257410ef3c4a8a1eb9b4ff416c0075d1c32860ca04913c8a9dace1de6a6", "https://esm.sh/@supabase/node-fetch@2.6.15/denonext/node-fetch.mjs": "0bae9052231f4f6dbccc7234d05ea96923dbf967be12f402764580b6bf9f713d", "https://esm.sh/@supabase/node-fetch@2.6.15?target=denonext": "4d28c4ad97328403184353f68434f2b6973971507919e9150297413664919cf3", "https://esm.sh/@supabase/postgrest-js@1.19.4/denonext/postgrest-js.mjs": "2073b5552ba10c7a8302bffffae771e3aede1daf833382355dae239fb0ab2576", "https://esm.sh/@supabase/realtime-js@2.11.2/denonext/realtime-js.mjs": "c33ac375b6be89c893f9df844d2525a4ace015a35aa6ba236270d00c6605c7ba", "https://esm.sh/@supabase/storage-js@2.7.1/denonext/storage-js.mjs": "73ac8cdc95cfcd794fe603dbd7ce06d539ab51538ae6467eabe0f9cc26c993aa", "https://esm.sh/@supabase/supabase-js@2.49.8": "fd72c6e822ed41d5fe7ad3bbe3a48420abbb21a579c73d532b36a6467f5b5f7d", "https://esm.sh/@supabase/supabase-js@2.49.8/denonext/supabase-js.mjs": "092ddc9030b46128e54ae6716a2356048015cdb98dc28eb26e60fa315a5d7a2a", "https://esm.sh/agent-base@7.1.3/denonext/agent-base.mjs": "65d873bec0946614e2e33c0c3c1c958ee346695facfdeb51e2dda348e700bd57", "https://esm.sh/agent-base@7.1.3?target=denonext": "c76a77281d852899060a3ecf8bed33525c5f252f53dfac18e2f0ae5957b22910", "https://esm.sh/asn1.js@5.4.1/denonext/asn1.mjs": "6b068713e7378406b25f431f6646ba10a0b03c5d72ecd68158f67bfe0867ffeb", "https://esm.sh/asn1.js@5.4.1?target=denonext": "8787df97afb1d0fcdee1fd4b1b9eb764a55f8327a51700b06c7059686399be46", "https://esm.sh/base64-js@1.5.1/denonext/base64-js.mjs": "5dbae8a1d8f5789da21736786f9921727523b5ccc64cbcf9f831331c381957dc", "https://esm.sh/base64-js@1.5.1?target=denonext": "7c2f775b41bd5df47fedaa143e6619625c9d81f34302d6aa030e20d9a7240633", "https://esm.sh/bignumber.js@9.3.0/denonext/bignumber.mjs": "677b0fd9a0cf102049f84b66623e993c044042e9e21b1c97e2143cca10e52b75", "https://esm.sh/bignumber.js@9.3.0?target=denonext": "838889e5768684d6354de99a317538889de5336f669a61879aad719a8b9d544f", "https://esm.sh/bn.js@4.12.2/denonext/bn.mjs": "20c89dda5b538d93e06ad1ca1a54617a4f19893aede56e16c0d0cb3a4e23b3c5", "https://esm.sh/bn.js@4.12.2?target=denonext": "acf676bc314a99e4bf92101e36bf269b6c28012a74431dfe10b12f196e1debe5", "https://esm.sh/buffer-equal-constant-time@1.0.1/denonext/buffer-equal-constant-time.mjs": "c83a9e435334af6863aef9f50bd7d1a855e95055ba8af6dde63eaf674791f64b", "https://esm.sh/buffer-equal-constant-time@1.0.1?target=denonext": "947592b99778297d371afb105a753b42beffb623ca47203b5c71caa820cf05af", "https://esm.sh/bufferutil@4.0.9/denonext/bufferutil.mjs": "13dca4d5bb2c68cbe119f880fa3bd785b9a81a8e02e0834dae604b4b85295cd8", "https://esm.sh/bufferutil@4.0.9?target=denonext": "e32574569ab438facfcc3f412c659b0719bbf05477136ca176938c9a3ac45125", "https://esm.sh/debug@4.4.1/denonext/debug.mjs": "f4ead1d2ba56c04c08e42abe96388337ddb17a197c16a7d896442b4fcb4d643e", "https://esm.sh/debug@4.4.1?target=denonext": "3c77699e6583e2c743e63bc0571647b963f1c82282b970df992b59fa585d76eb", "https://esm.sh/ecdsa-sig-formatter@1.0.11/denonext/ecdsa-sig-formatter.mjs": "08370379943e48e31cda373711fe5528f60b3d3d992fae7c0d520ef55607640e", "https://esm.sh/ecdsa-sig-formatter@1.0.11?target=denonext": "23aaf9c51f08bb7217303391c8a27e4c8fc69ac98fa1e7066ccbd633f8777c10", "https://esm.sh/extend@3.0.2/denonext/extend.mjs": "2fcb45893e5be70d629e3c4c6f3100ee1bba03bc672ece1d4ae57c3b88db3a71", "https://esm.sh/extend@3.0.2?target=denonext": "35f0cb416b1c2975321f6cfc0fc0e9ae13d3a83fe90705718880f502a2e22ea4", "https://esm.sh/firebase-admin@13.4.0/app": "5c9a8dc8be4ff29c1eb14fea74e8c9f6f53fe8119e29aa3f6112f91d69181c48", "https://esm.sh/firebase-admin@13.4.0/denonext/app.mjs": "fb7f46bc4425f7078dd6f878b97b971fdbc3fa278645a1a6a43f91e34a529a81", "https://esm.sh/firebase-admin@13.4.0/denonext/lib/app/credential-factory.mjs": "801684bc778c59ce95e47bb6c697d80f71a993475ec76cb7cc9061f2aca8eef8", "https://esm.sh/firebase-admin@13.4.0/denonext/lib/app/credential-internal.mjs": "d36b77ef013ce90f95037b846b6dd0f52a0eedbf7d56bf25ee63cb78f28855ac", "https://esm.sh/firebase-admin@13.4.0/denonext/lib/app/lifecycle.mjs": "f965e51e153b79955715b9cd10ad656129c313d783ada3ad33051da8647b661c", "https://esm.sh/firebase-admin@13.4.0/denonext/lib/messaging/messaging.mjs": "905a35c31e697c4507dd825939121d977099aa2e91b9a002d64a5890713f1b5f", "https://esm.sh/firebase-admin@13.4.0/denonext/lib/utils/api-request.mjs": "a6e7c87e22e04686f017911869a0e9b93f865f6f93028d3a5c74a6668306f2a9", "https://esm.sh/firebase-admin@13.4.0/denonext/lib/utils/deep-copy.mjs": "c34bab413fd0b3084fb1f177d833b5a6d9378010282b6edf60bf9ccb0e647916", "https://esm.sh/firebase-admin@13.4.0/denonext/lib/utils/error.mjs": "8d6f7e8e3f77a31364b0141c0474ab93652b4ce0e663482ce9618ff1fc0b2758", "https://esm.sh/firebase-admin@13.4.0/denonext/lib/utils/index.mjs": "816cd77a818740834cc9e7834ec421f58789428527d1d19043f2def205196d2a", "https://esm.sh/firebase-admin@13.4.0/denonext/lib/utils/validator.mjs": "989c06a33d63d3e582549a318812514558e77902e650fdb4eb01b6bcdbcf13eb", "https://esm.sh/firebase-admin@13.4.0/denonext/messaging.mjs": "6517d726378014809e4c8c3dd3749b3a9bdc76e167c50e8fb2c42dcd8e740c5c", "https://esm.sh/firebase-admin@13.4.0/messaging": "efb9484c4cf175c62b2a90c820f167343edfed1148bb9350db9bd11dda0fbf18", "https://esm.sh/gaxios@6.7.1/denonext/gaxios.mjs": "0905b5003dbbd93333551488c08b183796b2287d7ed51a76ff2eade9f8ab428c", "https://esm.sh/gaxios@6.7.1?target=denonext": "7a4c18a32b67699a87f8ba9327124b487b6361ed086b277ce3e8bbaf6dc58288", "https://esm.sh/gcp-metadata@6.1.1/denonext/gcp-metadata.mjs": "d49d451fa30de751d14c24fdc107b2f8ee3019162d4e8118f450b6484d33e4a6", "https://esm.sh/gcp-metadata@6.1.1?target=denonext": "e45a10ea50233c4ab40409aac730ba5d93c44ae7664759499238073432ec30aa", "https://esm.sh/google-auth-library@9.15.1/denonext/google-auth-library.mjs": "6aca9e17536e9cf512a36954d41c00ac345e297f457762e75663b66ef3c7114d", "https://esm.sh/google-auth-library@9.15.1?target=denonext": "e0d0b2e859a92db46807add60dddedc25c4d54cb4132752769522492fe3b60f4", "https://esm.sh/google-logging-utils@0.0.2/denonext/google-logging-utils.mjs": "932883c6dcbb056699e1feb2692b6e13a8d0dbc29c80e930db4085e8d41d6da8", "https://esm.sh/google-logging-utils@0.0.2?target=denonext": "112531bcd07a54193e4380c4e4ff99c67cc5c1f0dfd5d3b3fd5aadceac6827b8", "https://esm.sh/gtoken@7.1.0/denonext/gtoken.mjs": "cad6556c7d4e16dae5520ec1fd022843b44603c483193b9dfba28be90ba005b0", "https://esm.sh/gtoken@7.1.0?target=denonext": "1dde742f8ec44202729bab31ed24fb085222bd69aa8e491daecbbd34e5f2fb0b", "https://esm.sh/http_ece@1.1.0/denonext/http_ece.mjs": "5243370164c4486bdc04c96a866fbd2240e63b196ce00046480b312568ec33ee", "https://esm.sh/https-proxy-agent@7.0.6/denonext/https-proxy-agent.mjs": "37b0793e160738a4ae695ea567218fb5ad39c458c33ebd5857f06deb3605ffec", "https://esm.sh/https-proxy-agent@7.0.6?target=denonext": "cd42ba7e40cb2f053a2151f66585c0c338818807c0d7208e23a8302c15659b23", "https://esm.sh/is-stream@2.0.1/denonext/is-stream.mjs": "b5b524f43edbdd1bbd2890681d59ad56c2e9104c6aa23fcb5349abdd07824875", "https://esm.sh/is-stream@2.0.1?target=denonext": "c459897fef69f7059e31861bb5a4761a4388a412e84c6b9969c8ef833d4bff06", "https://esm.sh/json-bigint@1.0.0/denonext/json-bigint.mjs": "371978c3c309d7e3c514a819fbf0f8f3c69d7449000dd426c3bfa4adccc3f859", "https://esm.sh/json-bigint@1.0.0?target=denonext": "63fa057444cfc0577b04b30c01e5bf839f251afdac87d69e959a275081b2c7e6", "https://esm.sh/jwa@2.0.1/denonext/jwa.mjs": "9f886562cccb7bf4b6bb600597658ddb2d4f235ea7e00b534ff30ad33ae9a7d3", "https://esm.sh/jwa@2.0.1?target=denonext": "8a0c92b54c9d5bb54ce964bc189c11eb01316ef094b70cef107909bfa8de5eb5", "https://esm.sh/jws@4.0.0/denonext/jws.mjs": "3776ecd9ee3fc22e12a3d5766a27bf52fa6adb21ca5c126442a2da90fa5a09be", "https://esm.sh/jws@4.0.0?target=denonext": "a8a5e7e36b29e911e99a7a78bfe0ac37f5893a1bfc94360e269f842970dd70ad", "https://esm.sh/minimalistic-assert@1.0.1/denonext/minimalistic-assert.mjs": "07377d761438c22ed239e538e2b641cce8e0733793f0a98f34594782a6e85829", "https://esm.sh/minimalistic-assert@1.0.1?target=denonext": "4e19506c5a46f3c68b4d7b431e1de059c6c57b663cabf226cdb7ef0906b7aebe", "https://esm.sh/ms@2.1.3/denonext/ms.mjs": "9039464da1f4ae1c2042742d335c82556c048bbe49449b5d0cd5198193afa147", "https://esm.sh/ms@2.1.3?target=denonext": "36f5aa7503ff0ff44ce9e3155a60362d8d3ae5db8db048be5764a3a515b6a263", "https://esm.sh/node-forge@1.3.1/denonext/node-forge.mjs": "52285867c50dcf6577763a0d06899dde0eb5d0f60f5c331d1ce0ca9430f3a193", "https://esm.sh/node-forge@1.3.1?target=denonext": "39fbff1dd3f1f45bf7019492f7c7e941fcf91887cebbbd86c439100d06c6e348", "https://esm.sh/node-gyp-build@4.8.4/denonext/node-gyp-build.mjs": "9a86f2d044fc77bd60aaa3d697c2ba1b818da5fb1b9aaeedec59a40b8e908803", "https://esm.sh/node-gyp-build@4.8.4?target=denonext": "261a6cedf1fdbf159798141ba1e2311ac1510682c5c8b55dacc8cf5fdee4aa06", "https://esm.sh/safe-buffer@5.2.1/denonext/safe-buffer.mjs": "51b088d69d0bbf6d7ce4179853887e105715df40e432a3bff0e9575cc2285276", "https://esm.sh/safe-buffer@5.2.1?target=denonext": "34028b9647c849fa96dfd3d9f217a3adca8b43b13409820ac3f43fb15eba3e20", "https://esm.sh/safer-buffer@2.1.2/denonext/safer-buffer.mjs": "63b601ca3ed03a32349ca04538ac898f2c520dbc6b554246d11c482c31e8a6b8", "https://esm.sh/safer-buffer@2.1.2?target=denonext": "381ed15e73b07affd71d58c070213d027e4b8951c381f44e5f80589d3ea9957b", "https://esm.sh/supports-color@10.0.0/denonext/supports-color.mjs": "239cd39d0828e1a018dee102748da869b1b75c38fe6a9c0c8f0bd4ffbd3e1ea1", "https://esm.sh/supports-color@10.0.0?target=denonext": "4895255248e4ba0cbcce9437003dccf3658b1ac1d1e8eba5225fb8194c454ee1", "https://esm.sh/tr46@0.0.3/denonext/tr46.mjs": "5753ec0a99414f4055f0c1f97691100f13d88e48a8443b00aebb90a512785fa2", "https://esm.sh/tr46@0.0.3?target=denonext": "19cb9be0f0d418a0c3abb81f2df31f080e9540a04e43b0f699bce1149cba0cbb", "https://esm.sh/urlsafe-base64@1.0.0/denonext/urlsafe-base64.mjs": "bf3c627075ee9f5964c169cee97f25a393bb66d3e0a3e9356109e22c27dd0f82", "https://esm.sh/urlsafe-base64@1.0.0?target=denonext": "7385f984d2333db12ae993152ae9b5731e1e05dfb524e8b1f3adfd5bba5c1aa1", "https://esm.sh/utf-8-validate@6.0.5/denonext/utf-8-validate.mjs": "66b8ea532a0c745068f5b96ddb1bae332c3036703243541d2e89e66331974d98", "https://esm.sh/utf-8-validate@6.0.5?target=denonext": "071bc33ba1a58297e23a34d69dd589fd06df04b0f373b382ff5da544a623f271", "https://esm.sh/uuid@9.0.1/denonext/uuid.mjs": "25ffc9943d4b3691671de82d30c147ecfa2a8fae6f4319db28dadb0dacc19450", "https://esm.sh/uuid@9.0.1?target=denonext": "11c38693969f62a617de5ceee47ddc29ae892ae81491378a933436788fad3c8b", "https://esm.sh/web-push@3.6.6": "6a7a3942f0ce0c5354801cecbdfe5e71785f8df0adbadbb2db314db5994e0cc9", "https://esm.sh/web-push@3.6.6/denonext/web-push.mjs": "2ef919de8b597c167bb262d256c96aca38490b00b6e2b4ff7f9183169d6885f0", "https://esm.sh/webidl-conversions@3.0.1/denonext/webidl-conversions.mjs": "54b5c2d50a294853c4ccebf9d5ed8988c94f4e24e463d84ec859a866ea5fafec", "https://esm.sh/webidl-conversions@3.0.1?target=denonext": "4e20318d50528084616c79d7b3f6e7f0fe7b6d09013bd01b3974d7448d767e29", "https://esm.sh/whatwg-url@5.0.0/denonext/whatwg-url.mjs": "29b16d74ee72624c915745bbd25b617cfd2248c6af0f5120d131e232a9a9af79", "https://esm.sh/whatwg-url@5.0.0?target=denonext": "f001a2cadf81312d214ca330033f474e74d81a003e21e8c5d70a1f46dc97b02d", "https://esm.sh/ws@8.18.2/denonext/ws.mjs": "b9211ecb1511b09f418c1330920c66800b66710b2cd2997b64b7e0525bd895d2", "https://esm.sh/ws@8.18.2?target=denonext": "2ee7b1bb11543dda3e7e1c685ad8599b6f18aea785302374c3def5da468a1e51"}, "workspace": {"packageJson": {"dependencies": ["npm:@capacitor-community/contacts@7", "npm:@capacitor/android@^7.2.0", "npm:@capacitor/cli@^7.2.0", "npm:@capacitor/core@^7.2.0", "npm:@capacitor/ios@^7.2.0", "npm:@capacitor/push-notifications@7", "npm:@eslint/js@^9.22.0", "npm:@mdi/font@^7.4.47", "npm:@supabase/supabase-js@^2.39.0", "npm:@vite-pwa/assets-generator@1", "npm:@vitejs/plugin-vue@^5.2.3", "npm:@vue/eslint-config-prettier@^10.2.0", "npm:eslint-plugin-vue@10.0", "npm:eslint@^9.22.0", "npm:globals@16", "npm:papa<PERSON><PERSON>@^5.5.2", "npm:pinia@^3.0.1", "npm:prettier@3.5.3", "npm:vite-plugin-pwa@1", "npm:vite-plugin-vue-devtools@^7.7.2", "npm:vite@^6.2.4", "npm:vue-router@^4.5.0", "npm:vue@^3.5.13", "npm:vuetify@^3.8.2", "npm:workbox-build@^7.3.0", "npm:workbox-core@^7.3.0", "npm:workbox-precaching@^7.3.0", "npm:workbox-routing@^7.3.0", "npm:workbox-strategies@^7.3.0", "npm:workbox-window@^7.3.0"]}}}