# Apple Push Notification Service (APNS) Setup Guide

This guide will help you set up Apple Push Notification Service for ConfettiWish to enable push notifications on iOS devices.

## Prerequisites

- Apple Developer account ($99/year)
- Xcode installed on a Mac
- Access to the Apple Developer portal

## Steps

### 1. Create an App ID

1. Go to the [Apple Developer Portal](https://developer.apple.com/account/)
2. Navigate to "Certificates, IDs & Profiles"
3. Select "Identifiers" from the sidebar
4. Click the "+" button to register a new App ID
5. Select "App IDs" and click "Continue"
6. Fill in the form:
   - Description: "ConfettiWish"
   - Bundle ID: "io.confettiwish.app"
7. Scroll down to "Capabilities" and enable "Push Notifications"
8. Click "Continue" and then "Register"

### 2. Create a Push Notification Key

1. In the Apple Developer Portal, go to "Certificates, IDs & Profiles"
2. Select "Keys" from the sidebar
3. Click the "+" button to create a new key
4. Enter a name: "ConfettiWish Push Key"
5. Check the "Apple Push Notifications service (APNs)" checkbox
6. Click "Continue" and then "Register"
7. Download the key file (.p8) - **Important**: You can only download this file once!
8. Note the Key ID shown on the page

### 3. Configure Firebase for iOS Push Notifications

If you're using Firebase Cloud Messaging as a unified push service:

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Click the iOS icon to add an iOS app
4. Enter the bundle ID: "io.confettiwish.app"
5. Enter a nickname (optional): "ConfettiWish iOS"
6. Download the `GoogleService-Info.plist` file
7. Place it in the `confettiwish/ios/App/App/` directory

### 4. Configure APNs in Firebase

1. In the Firebase console, go to Project settings
2. Navigate to the "Cloud Messaging" tab
3. Under "Apple app configuration", click "Upload"
4. Upload the .p8 key file you downloaded earlier
5. Enter the Key ID
6. Enter your Apple Team ID (found in your Apple Developer account)

### 5. Configure Capacitor

1. Open `confettiwish/ios/App/App/Info.plist`
2. Add the following entries if they don't exist:
   ```xml
   <key>UIBackgroundModes</key>
   <array>
     <string>remote-notification</string>
   </array>
   ```

### 6. Update Entitlements

1. Open Xcode and open the iOS project in `confettiwish/ios/App`
2. Select the "App" target
3. Go to the "Signing & Capabilities" tab
4. Click "+ Capability" and add "Push Notifications"
5. Also add "Background Modes" and check "Remote notifications"

## Testing APNS

To test that APNS is working correctly:

1. Build and run your iOS app on a physical device (not simulator)
2. Make sure the app requests notification permissions
3. Use the test notification feature in the app settings
4. Check the Firebase console under "Cloud Messaging" to see delivery statistics

## Troubleshooting

- **Notifications not showing**: Check that the device has granted notification permissions
- **Token registration failing**: Verify the provisioning profile includes push capabilities
- **Certificate issues**: Ensure your Apple Developer account is active and the key is valid
- **Firebase errors**: Check Supabase Edge Function logs for detailed error messages

## Resources

- [Apple Push Notification Service Documentation](https://developer.apple.com/documentation/usernotifications)
- [Firebase Cloud Messaging for iOS](https://firebase.google.com/docs/cloud-messaging/ios/client)
- [Capacitor Push Notifications Plugin](https://capacitorjs.com/docs/apis/push-notifications)
- [Firebase Setup Guide](firebase-setup.md)
