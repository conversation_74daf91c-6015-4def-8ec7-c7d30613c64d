# Recommended Tech Stack (v1.1)
## Front-End
- Vue.js
    - Why?  a highly approachable and progressive JavaScript framework used to develop web user interfaces.

    - Benefits:
        - By integrating with Vuetify, one can have responsive material design components.
        - It allows users to combine CSS, JavaScript, and HTML in a single file.
        - For decent SPA development, it also includes a powerful Vue router.
        - It provides the authority to reuse components for scalable applications.
        - It promotes easy integration with modern tools and components, along with reactive data binding.
        - Vue.js is highly flexible, as it could be used for full-fledged SPA or small applications.

    - Role: Handles SPA UI, routing, and client-side logic.

    - Use latest version, 3.5+
        - Improved Reactivity System: Vue 3.5 introduces optimizations to the reactivity system, reducing the overhead of dependency tracking
        - Better Suspense Support: Suspense has been enhanced to provide more intuitive handling of asynchronous components, making it easier to load data or lazy-load components seamlessly
        - Server-Side Rendering (SSR) Enhancements: The SSR process in Vue 3.5 has been streamlined to reduce memory usage and improve rendering speed.

- Vuetify
    - Why? Vue.js component library implementing Google's Material Design.

    - Benefits:
        - Material Design components.
        - Responsive and mobile-first design.
        - Easy integration with Vue.js.
        - Large community and active development.

    - Role: Provides UI components for the SPA.

- Vite
    - Why? Vue's default build tool, offering a fast dev server and optimized production builds.

    - Benefits: Instant hot module replacement (HMR) and minimal build times.

    - Role: Build tool for the front-end.

- Pinia
    - Why? Vue's default state management library, offering a simple and lightweight solution.

    - Benefits:
        - Simplicity and ease of use.
        - Good performance.
        - Type safety with TypeScript.

    - Role: Manages application state.

- Capacitor
    - Why? Cross-platform app development framework.

    - Benefits:
        - Native app capabilities.
        - Easy integration with Vue.js.
        - Large community and active development.

    - Role: Enables building native mobile apps.

## Back-End
- Supabase
    - Why? Comprehensive backend-as-a-service with PostgreSQL, authentication, and real-time features.

    - Benefits:
        - Built-in authentication system
        - Real-time subscriptions
        - Row Level Security (RLS)
        - Database backups
        - Edge Functions
        - Storage solution
        - Auto-generated API
        - TypeScript support

    - Components:
        - PostgreSQL Database: Stores user profiles, friends, wishes, and app data
        - Supabase Auth: Handles email magic link authentication
        - Storage: Manages profile images and assets
        - Edge Functions: Handles complex business logic
        - Real-time: Enables live updates

## Authentication
- Supabase Auth with Email OTP
    - Why? Secure, password-less authentication with built-in email verification.

    - Benefits:
        - Built-in email templates
        - Automatic session management
        - JWT handling
        - Security best practices
        - User management dashboard
        - More secure than magic links
        - Better mobile experience
        - Reduced risk of email client security issues

    - Features:
        - Email OTP verification
        - 6-digit numeric codes
        - Configurable OTP expiration
        - Rate limiting
        - JWT tokens
        - Session management
        - Row Level Security integration
        - Brute force protection

    - Security Considerations:
        - OTP expiration time (default 5 minutes)
        - Rate limiting on OTP requests
        - Maximum attempts for OTP verification
        - Secure OTP transmission
        - Session invalidation on multiple failed attempts

## Notification System
- Use push notifications for birthday reminders and wish updates.
- Client apps communicate only with Supabase (no direct Firebase calls)
- Supabase Edge Functions handle all Firebase communication
- Background jobs process notifications from database table and send via Firebase
- Firebase delivers to Web, iOS, and Android devices
- Internal app notifications
    - Why? Real-time updates for user-specific events.

    - Benefits:
        - Instant feedback
        - Real-time updates
        - Customizable notifications
        - Integration with Supabase Realtime
        - User engagement
        - Better user experience

    - Components:
        - Supabase Realtime
        - Notification store
        - Notification components
        - Notification preferences
        - Notification sounds
        - Vibration support

- Brevo + Supabase Edge Functions
    - Why? Enterprise-grade email service with powerful templating and automation features.

    - Benefits:
        - Reliable email delivery
        - Advanced templating system
        - Email tracking and analytics
        - Contact management
        - Automation workflows
        - Comprehensive API
        - Webhook support
        - GDPR compliant

    - Components:
        - Brevo API integration
        - Edge Functions for email logic
        - Custom email templates
        - Contact synchronization
        - Email scheduling system
        - Analytics dashboard

    - Features:
        - Transactional emails
        - Marketing campaigns (future)
        - Email tracking
        - Template management
        - Contact lists
        - Automation workflows

## Integration Architecture
### Email Flow
1. Trigger Event (e.g., upcoming birthday)
2. Supabase Edge Function processes event
3. Edge Function calls Brevo API
4. Brevo sends email using appropriate template
5. Webhook receives delivery/open status
6. Status stored in Supabase

### Contact Sync Flow
1. User profile updated
2. Edge Function triggered
3. Contact synced to Brevo
4. Contact attributes updated
5. Segmentation updated

## System Components
[Previous components remain unchanged]

## Updated Diagram (Conceptual)

[User Browser/Device] <--> [Vue]
                              |
                              | HTTPS/WebSocket
                              v
                        [Supabase Platform]
                              |
                        --------------------------------
                        |             |                 |
                    [Auth/DB]    [Edge Functions]   [Background Jobs]
                                      |                 |
                                      v                 v
                                 [Brevo API]        [Firebase]
                                      |                 |
                              [Email Delivery]   [Push Notifications]

# Summary
This architecture leverages Supabase's comprehensive platform to provide a secure, scalable, and maintainable application. The combination of Vue.js and Supabase enables rapid development while maintaining enterprise-grade security and performance.

Key Benefits:
1. Simplified infrastructure management
2. Built-in security best practices
3. Real-time capabilities
4. Automatic API generation
5. Type safety with TypeScript
6. Scalable serverless architecture

Security Features:
1. Row Level Security (RLS)
2. Secure session management
3. Email authentication
4. API rate limiting
5. Secure asset storage
6. Database encryption
7. Automatic backups
8. Audit logs

