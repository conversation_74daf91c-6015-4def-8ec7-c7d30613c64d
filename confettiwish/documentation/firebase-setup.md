# Firebase Cloud Messaging Setup Guide

This guide will help you set up Firebase Cloud Messaging (FCM) for ConfettiWish to enable push notifications on Android devices and web browsers.

## Prerequisites

- Google account
- Access to the Firebase console
- Android Studio (for testing)

## Steps

### 1. Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project"
3. Enter "ConfettiWish" as the project name
4. Follow the setup wizard (you can disable Google Analytics if not needed)
5. Click "Create project"

### 2. Register Your Android App

1. In the Firebase console, click the Android icon to add an Android app
2. Enter the package name: `io.confettiwish.app`
3. Enter a nickname (optional): "ConfettiWish Android"
4. Enter the SHA-1 signing certificate (optional for development)
5. Click "Register app"

### 3. Download Configuration File

1. Download the `google-services.json` file
2. Place it in the `confettiwish/android/app/` directory

### 4. Add Web App to Firebase

1. In the Firebase console, click the Web icon to add a web app
2. Enter "ConfettiWish Web" as the app nickname
3. Check "Also set up Firebase Hosting" if you plan to use Firebase Hosting
4. Click "Register app"
5. Copy the Firebase configuration object for later use

### 5. Configure Supabase Edge Functions

1. Generate a Firebase Admin SDK private key:
   - Go to Project Settings > Service accounts
   - Click "Generate new private key"
   - Save the JSON file securely

2. Add the Firebase service account to Supabase:
   - Go to your Supabase project dashboard
   - Navigate to Settings > API
   - Under "Project Settings", find "Environment Variables"
   - Add a new secret:
     - Name: `FIREBASE_SERVICE_ACCOUNT`
     - Value: Paste the entire JSON content of your Firebase service account key

3. Add Firebase VAPID key for web push:
   - In Firebase console, go to Project Settings > Cloud Messaging
   - Under "Web configuration", find your "Web Push certificates"
   - Generate a new key pair if needed
   - Add to Supabase environment variables:
     - Name: `FIREBASE_PUBLIC_KEY`
     - Value: Your VAPID public key

4. Deploy the Edge Functions:
   ```bash
   cd confettiwish
   supabase functions deploy send-notifications
   supabase functions deploy get-notification-token
   ```

### 6. Configure Client Environment Variables

Add the following to your `.env` file:

```env
VITE_FIREBASE_VAPID_KEY=your-vapid-public-key-here
```

## Architecture

The notification system works as follows:

1. **Client Registration**: Apps register for notifications through Supabase
2. **Token Storage**: Device tokens are stored in Supabase database
3. **Notification Creation**: Events create entries in the notifications table
4. **Background Jobs**: Supabase jobs process notifications and send via Firebase
5. **Delivery**: Firebase delivers notifications to devices

## Testing FCM

To test that FCM is working correctly:

1. Build and run your Android app on a device or emulator
2. Make sure the app requests notification permissions
3. Use the test notification feature in the app settings
4. Check the Firebase console under "Cloud Messaging" to see delivery statistics

## Troubleshooting

- **Notifications not showing**: Check that the device has granted notification permissions
- **Token registration failing**: Verify the `google-services.json` file is in the correct location
- **Edge function errors**: Check Supabase logs for detailed error messages

## Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Firebase Admin SDK Documentation](https://firebase.google.com/docs/admin/setup)
- [Capacitor Push Notifications Plugin](https://capacitorjs.com/docs/apis/push-notifications)
