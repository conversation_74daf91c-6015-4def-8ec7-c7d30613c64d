# Notification Jobs Documentation

This document describes the notification system workflow and the background jobs that handle notification processing in ConfettiWish.

## Architecture Overview

The notification system follows this workflow:

1. **Event Triggers** → Create entries in `notifications` table
2. **Background Jobs** → Process notifications and send via Firebase
3. **Firebase** → Delivers to Web, iOS, and Android devices

## Database Schema

### Notifications Table

```sql
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL,
  title VARCHAR(255) NOT NULL,
  body TEXT NOT NULL,
  data JSONB DEFAULT '{}',
  scheduled_for TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  sent_at TIMESTAMP WITH TIME ZONE,
  status VARCHAR(20) DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_scheduled_for ON notifications(scheduled_for);
CREATE INDEX idx_notifications_type ON notifications(type);
```

### Notification Types

- `birthday_reminder` - Birthday reminders
- `wish_update` - Wishlist changes
- `friend_activity` - Friend-related activities
- `system` - System notifications

### Notification Status

- `pending` - Not yet sent
- `sent` - Successfully sent
- `failed` - Failed to send
- `cancelled` - Cancelled before sending

## Background Jobs

### 1. Notification Processor Job

**Purpose**: Process pending notifications and send them via Firebase
**Frequency**: Every 5 minutes
**Function**: `process-notifications`

```typescript
// supabase/functions/process-notifications/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  // Get pending notifications that are due
  const { data: notifications, error } = await supabase
    .from('notifications')
    .select('*')
    .eq('status', 'pending')
    .lte('scheduled_for', new Date().toISOString())
    .order('scheduled_for', { ascending: true })
    .limit(100)

  if (error) {
    console.error('Error fetching notifications:', error)
    return new Response(JSON.stringify({ error: error.message }), { status: 500 })
  }

  const results = []
  
  for (const notification of notifications) {
    try {
      // Send notification via Firebase
      const response = await supabase.functions.invoke('send-notifications', {
        body: {
          type: notification.type,
          userId: notification.user_id,
          data: {
            title: notification.title,
            body: notification.body,
            data: notification.data
          }
        }
      })

      if (response.error) {
        // Mark as failed
        await supabase
          .from('notifications')
          .update({ 
            status: 'failed', 
            updated_at: new Date().toISOString() 
          })
          .eq('id', notification.id)
        
        results.push({ id: notification.id, status: 'failed', error: response.error })
      } else {
        // Mark as sent
        await supabase
          .from('notifications')
          .update({ 
            status: 'sent', 
            sent_at: new Date().toISOString(),
            updated_at: new Date().toISOString() 
          })
          .eq('id', notification.id)
        
        results.push({ id: notification.id, status: 'sent' })
      }
    } catch (error) {
      console.error(`Error processing notification ${notification.id}:`, error)
      results.push({ id: notification.id, status: 'error', error: error.message })
    }
  }

  return new Response(JSON.stringify({ 
    processed: results.length,
    results 
  }), { 
    headers: { 'Content-Type': 'application/json' } 
  })
})
```

### 2. Birthday Reminder Job

**Purpose**: Create birthday reminder notifications
**Frequency**: Daily at 9:00 AM
**Function**: `birthday-reminders`

```typescript
// supabase/functions/birthday-reminders/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  // Get upcoming birthdays (next 7 days)
  const today = new Date()
  const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)

  const { data: upcomingBirthdays, error } = await supabase
    .from('friends')
    .select(`
      id,
      name,
      birth_date,
      user_id,
      profiles!inner(notification_preferences)
    `)
    .gte('birth_date', today.toISOString().split('T')[0])
    .lte('birth_date', nextWeek.toISOString().split('T')[0])

  if (error) {
    console.error('Error fetching birthdays:', error)
    return new Response(JSON.stringify({ error: error.message }), { status: 500 })
  }

  const notifications = []

  for (const friend of upcomingBirthdays) {
    // Check if user has birthday reminders enabled
    if (!friend.profiles.notification_preferences?.birthday_reminders) {
      continue
    }

    // Check if notification already exists
    const { data: existing } = await supabase
      .from('notifications')
      .select('id')
      .eq('user_id', friend.user_id)
      .eq('type', 'birthday_reminder')
      .eq('data->friend_id', friend.id)
      .gte('created_at', today.toISOString().split('T')[0])

    if (existing && existing.length > 0) {
      continue // Already created today
    }

    // Calculate days until birthday
    const birthDate = new Date(friend.birth_date)
    const daysUntil = Math.ceil((birthDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

    let title, body
    if (daysUntil === 0) {
      title = `🎉 ${friend.name}'s Birthday Today!`
      body = `Don't forget to wish ${friend.name} a happy birthday!`
    } else if (daysUntil === 1) {
      title = `🎂 ${friend.name}'s Birthday Tomorrow`
      body = `${friend.name}'s birthday is tomorrow. Time to prepare!`
    } else {
      title = `📅 Upcoming Birthday`
      body = `${friend.name}'s birthday is in ${daysUntil} days`
    }

    // Create notification
    const { data: notification, error: notificationError } = await supabase
      .from('notifications')
      .insert({
        user_id: friend.user_id,
        type: 'birthday_reminder',
        title,
        body,
        data: {
          friend_id: friend.id,
          friend_name: friend.name,
          birth_date: friend.birth_date,
          days_until: daysUntil,
          url: `/friends/${friend.id}`
        },
        scheduled_for: new Date().toISOString()
      })
      .select()
      .single()

    if (notificationError) {
      console.error('Error creating notification:', notificationError)
    } else {
      notifications.push(notification)
    }
  }

  return new Response(JSON.stringify({ 
    created: notifications.length,
    notifications 
  }), { 
    headers: { 'Content-Type': 'application/json' } 
  })
})
```

## Cron Jobs Setup

Set up the following cron jobs in Supabase:

### 1. Process Notifications (Every 5 minutes)
```sql
SELECT cron.schedule(
  'process-notifications',
  '*/5 * * * *',
  $$
  SELECT net.http_post(
    url := 'https://your-project.supabase.co/functions/v1/process-notifications',
    headers := '{"Authorization": "Bearer ' || current_setting('app.service_role_key') || '"}'::jsonb
  );
  $$
);
```

### 2. Birthday Reminders (Daily at 9:00 AM)
```sql
SELECT cron.schedule(
  'birthday-reminders',
  '0 9 * * *',
  $$
  SELECT net.http_post(
    url := 'https://your-project.supabase.co/functions/v1/birthday-reminders',
    headers := '{"Authorization": "Bearer ' || current_setting('app.service_role_key') || '"}'::jsonb
  );
  $$
);
```

## Event Triggers

### Wishlist Changes

When a wishlist is updated, create a notification:

```sql
-- Trigger function
CREATE OR REPLACE FUNCTION notify_wish_update()
RETURNS TRIGGER AS $$
BEGIN
  -- Only notify if wish is shared and not the owner
  IF NEW.shared = true AND NEW.user_id != auth.uid() THEN
    INSERT INTO notifications (
      user_id,
      type,
      title,
      body,
      data,
      scheduled_for
    ) VALUES (
      NEW.user_id,
      'wish_update',
      'Wishlist Updated',
      'Someone updated a wish on your shared wishlist',
      jsonb_build_object(
        'wish_id', NEW.id,
        'url', '/wishes/' || NEW.id
      ),
      NOW()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger
CREATE TRIGGER wish_update_notification
  AFTER UPDATE ON wishes
  FOR EACH ROW
  EXECUTE FUNCTION notify_wish_update();
```

## Monitoring and Maintenance

### Cleanup Old Notifications

Run weekly to clean up old notifications:

```sql
-- Delete notifications older than 30 days
DELETE FROM notifications 
WHERE created_at < NOW() - INTERVAL '30 days';
```

### Monitor Failed Notifications

Query to check for failed notifications:

```sql
SELECT 
  type,
  COUNT(*) as failed_count,
  DATE(created_at) as date
FROM notifications 
WHERE status = 'failed' 
  AND created_at > NOW() - INTERVAL '7 days'
GROUP BY type, DATE(created_at)
ORDER BY date DESC, failed_count DESC;
```

## Testing

Test the notification system:

1. **Manual Trigger**: Call the Edge Functions directly
2. **Database Insert**: Insert test notifications directly
3. **Cron Test**: Manually trigger cron jobs

```bash
# Test notification processing
curl -X POST https://your-project.supabase.co/functions/v1/process-notifications \
  -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY"

# Test birthday reminders
curl -X POST https://your-project.supabase.co/functions/v1/birthday-reminders \
  -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY"
```
