// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Firebase Admin SDK for getting notification token
import { initializeApp } from 'https://esm.sh/firebase-admin/app'
import { getMessaging } from 'https://esm.sh/firebase-admin/messaging'

// Initialize Firebase Admin with service account
try {
  const serviceAccount = Deno.env.get("FIREBASE_SERVICE_ACCOUNT");
  if (!serviceAccount) {
    console.error("FIREBASE_SERVICE_ACCOUNT environment variable is not set");
    throw new Error("FIREBASE_SERVICE_ACCOUNT is required");
  }

  const serviceAccountJson = JSON.parse(serviceAccount);

  initializeApp({
    credential: {
      client_email: serviceAccountJson.client_email,
      private_key: serviceAccountJson.private_key,
      project_id: serviceAccountJson.project_id
    }
  });

  console.log("Firebase Admin SDK initialized successfully");
} catch (error) {
  console.error("Firebase initialization error:", error);
  throw error;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Max-Age': '86400'
      }
    })
  }

  try {
    // Create a Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the request body
    const { userId, platform } = await req.json()

    // Validate request
    if (!userId || !platform) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        { headers: { 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // For web platform, we don't generate tokens server-side
    // The client will generate the FCM registration token using Firebase SDK
    // This endpoint is mainly for mobile platforms or token validation

    if (platform === 'web') {
      // For web, return a success response - the client handles token generation
      return new Response(
        JSON.stringify({
          message: 'Web notifications should be registered client-side using Firebase SDK',
          success: true
        }),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          }
        }
      )
    }

    // For mobile platforms, we can generate tokens if needed
    // But typically mobile apps generate their own FCM tokens
    throw new Error('Token generation not implemented for platform: ' + platform)
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('Error getting notification token:', error)
    return new Response(
      JSON.stringify({ error: errorMessage }),
      { headers: { 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})

// To invoke:
// curl -i --location --request POST 'http://localhost:54321/functions/v1/get-notification-token' \
//   --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
//   --header 'Content-Type: application/json' \
//   --data-raw '{"userId": "user-id", "platform": "web"}'
