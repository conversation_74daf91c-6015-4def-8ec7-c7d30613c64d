# confettiwish

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```

## Push Notifications

ConfettiWish supports push notifications for birthday reminders and other important events:

- **Architecture**: Client → Supabase → Firebase → Devices
- **Mobile**: Uses Capacitor Push Notifications plugin with Firebase Cloud Messaging
- **Web**: Uses Firebase Cloud Messaging for browser notifications
- **Background Jobs**: Supabase processes notifications and sends via Firebase
- **Cross-platform**: Unified notification system works across Web, iOS, and Android

For setup instructions, see:
- [Firebase Setup Guide](docs/firebase-setup.md)
- [Notification Jobs Documentation](docs/notification-jobs.md)
